<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle">
    <!-- 设置圆角半径 -->
    <corners android:radius="15dp" /> <!-- 调整数值控制圆角弧度 -->
    <!-- 设置按钮背景色 -->
    <solid android:color="@color/grey_background" /> <!-- 替换为你的按钮颜色 -->
    <!-- 可选：设置内边距（避免文字紧贴边缘） -->
    <padding
        android:left="16dp"
        android:top="0dp"
        android:right="16dp"
        android:bottom="0dp" />
</shape>