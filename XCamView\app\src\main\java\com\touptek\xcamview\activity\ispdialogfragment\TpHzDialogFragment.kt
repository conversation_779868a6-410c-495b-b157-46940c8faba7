package com.touptek.xcamview.activity.ispdialogfragment
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.RadioGroup
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.touptek.xcamview.R
import com.touptek.video.TpIspParam

class TpHzDialogFragment : DialogFragment(){

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.hz_layout, container, false)

        initTpIspParam()
        initRadioGroup(view)
        initParameterValue(view)

        TpIspParam.addOnDataChangedListener(object : TpIspParam.OnDataChangedListener {
            override fun onDataChanged(param: TpIspParam, newValue: Int) {
            }

            override fun onLongDataChanged(param: TpIspParam, newValue: Long) {
            }
        })
        return view
    }


    override fun onStart() {
        super.onStart()
        val dialog = dialog
        if (dialog != null) {
            val window = dialog.window
            if (window != null) {
                window.setDimAmount(0f)
                window.setBackgroundDrawableResource(android.R.color.transparent)
                val params = window.attributes

                // 获取传递过来的位置和宽度参数
                val args = arguments
                if (args != null) {
                    val buttonX = args.getInt("x", 0)
                    val buttonY = args.getInt("y", 0)
                    val buttonWidth = args.getInt("width", 0)

                    // 设置对话框的位置，使其显示在按钮的右边
                    params.x = buttonX + buttonWidth + 25 // 右边偏移按钮宽度
                    params.y = buttonY - 70 // 同按钮的Y轴位置
                }

                params.width = WindowManager.LayoutParams.WRAP_CONTENT
                params.height = WindowManager.LayoutParams.WRAP_CONTENT
                params.gravity = Gravity.TOP or Gravity.START // 从左上角开始定位
                window.attributes = params
            }
        }
    }

    private fun initTpIspParam() {
//        TpIspParam.init(context)
//        // 设置串口状态变化监听器
//        TpIspParam.setOnSerialStateChangedListener { connected ->
//            if (connected){
//                showToast("串口已连接")
//            }else{
//                showToast("串口断开")
//            }
//        }
    }

    private fun showToast(message: String) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }

    private fun initRadioGroup(view: View) {
        val radioGroup = view.findViewById<RadioGroup>(R.id.hz_btn_group)
        radioGroup.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.radio_ac_50hz_tv -> {
                    // 选择了第一个 RadioButton
                    TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_HZ, 1)
                }
                R.id.radio_ac_60hz_tv -> {
                    // 选择了第二个 RadioButton
                    TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_HZ, 0)
                }
                R.id.radio_dc_tv -> {
                    // 选择了第三个 RadioButton
                    TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_HZ, 2)
                }
            }
        }
    }

    private fun initParameterValue(view: View) {
        //设置WB模式
        val radioGroup = view.findViewById<RadioGroup>(R.id.hz_btn_group)
        val savedSelection = TpIspParam.getCurrentValue(TpIspParam.TOUPTEK_PARAM_HZ)
        when (savedSelection) {
            0 -> radioGroup.check(R.id.radio_ac_60hz_tv)
            1 -> radioGroup.check(R.id.radio_ac_50hz_tv)
            2 -> radioGroup.check(R.id.radio_dc_tv)
        }

    }
}