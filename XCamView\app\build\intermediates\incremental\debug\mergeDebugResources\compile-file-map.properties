#Mon Jul 28 09:22:21 CST 2025
com.touptek.xcamview.app-main-43\:/color/red.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_red.xml.flat
com.touptek.xcamview.app-main-43\:/color/tab_text_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_tab_text_color.xml.flat
com.touptek.xcamview.app-main-43\:/drawable-v24/ic_launcher_foreground.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-v24_ic_launcher_foreground.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/about_d.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_about_d.png.flat
com.touptek.xcamview.app-main-43\:/drawable/about_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_about_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/add_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_add_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/bg_rounded_dialog_light.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_rounded_dialog_light.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/border_box.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_border_box.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/bottom_panel_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bottom_panel_background.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/brow_d.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_brow_d.png.flat
com.touptek.xcamview.app-main-43\:/drawable/browser_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_browser_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_about_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_about_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_about_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_about_pressed.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_color_adjustment_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_color_adjustment_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_color_adjustment_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_color_adjustment_pressed.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_confirm_bg.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_confirm_bg.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_draw_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_draw_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_draw_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_draw_pressed.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_exposure_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_exposure_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_exposure_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_exposure_pressed.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_flip_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_flip_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_flip_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_flip_pressed.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_folder_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_folder_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_folder_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_folder_pressed.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_image_processing_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_image_processing_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_image_processing_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_image_processing_pressed.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_menu_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_menu_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_menu_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_menu_pressed.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_pause_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_pause_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_pause_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_pause_pressed.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_power_frequency_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_power_frequency_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_power_frequency_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_power_frequency_pressed.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_record_video_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_record_video_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_record_video_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_record_video_pressed.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_rounded_default.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_rounded_default.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_settings_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_settings_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_settings_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_settings_pressed.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_take_photo_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_take_photo_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_take_photo_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_take_photo_pressed.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_white_balance_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_white_balance_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_white_balance_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_white_balance_pressed.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_zoom_in_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_zoom_in_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_zoom_in_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_zoom_in_pressed.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_zoom_out_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_zoom_out_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/btn_zoom_out_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_btn_zoom_out_pressed.png.flat
com.touptek.xcamview.app-main-43\:/drawable/button_border_selected.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_border_selected.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/config_d.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_config_d.png.flat
com.touptek.xcamview.app-main-43\:/drawable/config_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_config_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/delete_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_delete_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/dialog_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dialog_background.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/dialog_border.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dialog_border.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/divider.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_divider.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/divider_vertical_light.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_divider_vertical_light.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/exposure_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_exposure_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/flip_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_flip_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/freeze_d.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_freeze_d.png.flat
com.touptek.xcamview.app-main-43\:/drawable/freeze_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_freeze_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/grey_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_grey_background.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/groupbox_border.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_groupbox_border.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/groupbox_title_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_groupbox_title_background.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/home_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_home_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/hz_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_hz_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_about.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_about.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_action1.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_action1.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_action2.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_action2.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_action3.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_action3.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_action4.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_action4.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_action5.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_action5.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_allselect.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_allselect.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_angle_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_angle_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_annotation_arrow_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_annotation_arrow_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_annulus2_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_annulus2_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_annulus_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_annulus_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_arbline_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arbline_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_arc_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arc_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_arrowleft.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arrowleft.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_arrowright_d.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arrowright_d.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_calibration_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_calibration_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_cancel.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_cancel.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_cancel_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_cancel_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_centerc_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_centerc_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_checked.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_checked.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_close.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_close.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_close_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_close_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_color_adjust.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_color_adjust.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_color_adjustment.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_color_adjustment.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_config_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_config_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_copy.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_copy.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_cut.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_cut.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_delete_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_delete_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_details.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_details.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_draw.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_draw.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_ellipse_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_ellipse_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_export_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_export_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_exposure.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_exposure.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_fiveellipse_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_fiveellipse_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_flip.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_flip.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_fold.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_fold.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_fold_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_fold_pressed.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_folder.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_folder.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_fourptangle_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_fourptangle_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_hline_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_hline_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_image_processing.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_image_processing.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_launcher_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_lock_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_lock_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_menu.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_menu.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_parallel_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_parallel_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_paste.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_paste.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_pause.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_pause.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_pic.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_pic.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_picture.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_picture.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_point_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_point_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_polygon_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_polygon_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_power_frequency.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_power_frequency.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_preview.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_preview.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_record_start.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_record_start.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_record_video.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_record_video.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_rectangle_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_rectangle_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_return.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_return.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_scale_bar_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_scale_bar_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_select_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_select_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_settings.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_settings.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_storage.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_storage.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_take_photo.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_take_photo.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_text_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_text_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_threecircle_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_threecircle_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_threeline_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_threeline_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_threepttwowircles_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_threepttwowircles_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_threerectangle_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_threerectangle_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_threevertical_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_threevertical_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_unchecked.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_unchecked.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_video.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_video.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_video_triangle.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_video_triangle.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_vline_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_vline_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_wb_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_wb_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_white.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_white.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_white_balance.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_white_balance.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_zoom_in.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_zoom_in.png.flat
com.touptek.xcamview.app-main-43\:/drawable/ic_zoom_out.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_zoom_out.png.flat
com.touptek.xcamview.app-main-43\:/drawable/image_border_normal.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_image_border_normal.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/imageprocess_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_imageprocess_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/isp_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_isp_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/measure_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_measure_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/nav_separator.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_nav_separator.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/next_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_next_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/oval_button_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_oval_button_background.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/popup_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_popup_background.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/record_start_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_record_start_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/rounded_border.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_rounded_border.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/rounded_button_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_rounded_button_background.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/scenechange_d.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_scenechange_d.png.flat
com.touptek.xcamview.app-main-43\:/drawable/scenechange_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_scenechange_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/selector_settings_tab.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_selector_settings_tab.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/selector_tab_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_selector_tab_background.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/snap_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_snap_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/status_banner_bg.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_status_banner_bg.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/stepframe_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_stepframe_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/sub_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_sub_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/tab_selected_bg.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tab_selected_bg.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/thumb_blue.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_thumb_blue.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/thumb_blue_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_thumb_blue_selector.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/title_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_title_background.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/tp_custom_enabled_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_custom_enabled_selector.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/tp_custom_radionbutton.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_custom_radionbutton.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/tp_custom_seekbar.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_custom_seekbar.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/tp_custom_seekbar_thumb.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_custom_seekbar_thumb.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/tp_switch_thumb.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_switch_thumb.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/tp_switch_track.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_switch_track.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/track_blue_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_track_blue_selector.xml.flat
com.touptek.xcamview.app-main-43\:/drawable/zoomin_d.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_zoomin_d.png.flat
com.touptek.xcamview.app-main-43\:/drawable/zoomin_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_zoomin_n.png.flat
com.touptek.xcamview.app-main-43\:/drawable/zoomout_d.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_zoomout_d.png.flat
com.touptek.xcamview.app-main-43\:/drawable/zoomout_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_zoomout_n.png.flat
com.touptek.xcamview.app-main-43\:/font/kai.ttf=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_kai.ttf.flat
com.touptek.xcamview.app-main-43\:/font/song.ttf=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_song.ttf.flat
com.touptek.xcamview.app-main-43\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.touptek.xcamview.app-main-43\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.touptek.xcamview.app-main-43\:/mipmap-hdpi/ic_launcher.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.png.flat
com.touptek.xcamview.app-main-43\:/mipmap-hdpi/ic_launcher_round.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.png.flat
com.touptek.xcamview.app-main-43\:/mipmap-mdpi/ic_launcher.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.png.flat
com.touptek.xcamview.app-main-43\:/mipmap-mdpi/ic_launcher_round.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.png.flat
com.touptek.xcamview.app-main-43\:/mipmap-xhdpi/ic_launcher.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.png.flat
com.touptek.xcamview.app-main-43\:/mipmap-xhdpi/ic_launcher_round.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.png.flat
com.touptek.xcamview.app-main-43\:/mipmap-xxhdpi/ic_launcher.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.png.flat
com.touptek.xcamview.app-main-43\:/mipmap-xxhdpi/ic_launcher_round.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.png.flat
com.touptek.xcamview.app-main-43\:/mipmap-xxxhdpi/ic_launcher.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.png.flat
com.touptek.xcamview.app-main-43\:/mipmap-xxxhdpi/ic_launcher_round.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.png.flat
com.touptek.xcamview.app-main-43\:/xml/backup_rules.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.touptek.xcamview.app-main-43\:/xml/data_extraction_rules.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/activity_main.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/activity_touptek.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_touptek.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/activity_touptek_btn.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_touptek_btn.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/activity_welcome.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_welcome.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/autoae_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_autoae_layout.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/browse_grid_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_browse_grid_layout.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/browse_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_browse_layout.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/copydialog_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_copydialog_settings.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/dialog_file_details.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_file_details.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/dialog_modern_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_modern_settings.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/dialog_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_settings.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/flip_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_flip_layout.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/folder_item.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_folder_item.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/fragment_format_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_format_settings.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/fragment_measurement_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_measurement_settings.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/fragment_network_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_network_settings.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/fragment_storage_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_storage_settings.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/hz_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_hz_layout.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/image_parameter_2_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_image_parameter_2_layout.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/image_parameter_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_image_parameter_layout.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/image_viewer.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_image_viewer.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/layout_input_info_item.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_layout_input_info_item.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/measurement_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_measurement_layout.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/operation_grid_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_operation_grid_layout.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/popup_config_menu.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_popup_config_menu.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/popup_menu_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_popup_menu_layout.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/right_panel_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_right_panel_layout.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/scene_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_scene_layout.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/settings_misc.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_settings_misc.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/settings_record.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_settings_record.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/testdialog_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_testdialog_settings.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/video_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_video_layout.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/videodecode_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_videodecode_layout.xml.flat
com.touptek.xcamview.app-mergeDebugResources-40\:/layout/whitebalance_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_whitebalance_layout.xml.flat
