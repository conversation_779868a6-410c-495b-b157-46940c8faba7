package com.touptek.test.ui;

import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentActivity;

import com.android.rockchip.mediacodecnew.R;

import com.touptek.video.TpIspParam;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * TpTestDialog - ISP场景管理测试对话框
 * <p>
 * 提供简洁的弹窗界面来测试ISP场景管理功能，包括场景的保存、加载、
 * 列表查看和完整测试流程。采用异步执行避免阻塞UI线程。
 * </p>
 * 
 */
public class TpTestDialog extends DialogFragment {
    private static final String TAG = "TpTestDialog";
    
    // ===== UI组件 =====

    private Button btnSaveScene;
    private Button btnLoadScene;

    private Button btnDeleteScene;

    // 系统场景按钮
    private Button btnBiologicalScene;
    private Button btnStereoscopicScene;


    private ProgressBar progressBar;

    // ISP调节界面组件
    private android.widget.LinearLayout ispAdjustLayout;

    // ISP参数监听和控件映射
    private TpIspParam.OnDataChangedListener ispDataChangedListener;
    private java.util.Map<TpIspParam, ParameterControlPair> parameterControlMap;
    
    // ===== 测试状态 =====
    
    private boolean isTestRunning = false;
    private Handler mainHandler;
    
    // ===== 静态工厂方法 =====
    
    /**
     * 创建并显示测试对话框
     *
     * @param context 上下文，必须是FragmentActivity
     */
    public static void show(Context context) {
        if (context instanceof FragmentActivity) {
            FragmentActivity activity = (FragmentActivity) context;

            TpTestDialog dialog = new TpTestDialog();
            dialog.show(activity.getSupportFragmentManager(), "TpTestDialog");
        } else {
            throw new IllegalArgumentException("Context必须是FragmentActivity的实例");
        }
    }

    
    // ===== 生命周期方法 =====
    
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mainHandler = new Handler(Looper.getMainLooper());
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

        // 移除ISP参数监听器
        if (ispDataChangedListener != null) {
            TpIspParam.removeOnDataChangedListener(ispDataChangedListener);
        }

        // 清理资源
        if (mainHandler != null) {
            mainHandler.removeCallbacksAndMessages(null);
        }

        // 清理映射
        if (parameterControlMap != null) {
            parameterControlMap.clear();
        }
    }
    
    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        AlertDialog.Builder builder = new AlertDialog.Builder(requireContext());
        
        // 加载自定义布局
        LayoutInflater inflater = requireActivity().getLayoutInflater();
        View view = inflater.inflate(R.layout.dialog_tp_test, null);
        
        // 初始化UI组件
        initViews(view);
        setupListeners();
        
        builder.setView(view)
               .setTitle("ToupTek SDK 测试功能")
               .setNegativeButton("关闭", (dialog, which) -> dismiss());
        
        return builder.create();
    }
    
    @Override
    public void onStart() {
        super.onStart();
        // 设置对话框大小
        if (getDialog() != null && getDialog().getWindow() != null) {
            int width = (int) (getResources().getDisplayMetrics().widthPixels * 0.9);
            int height = (int) (getResources().getDisplayMetrics().heightPixels * 0.75);
            getDialog().getWindow().setLayout(width, height);
        }
    }
    
    // ===== UI初始化方法 =====
    
    /**
     * 初始化UI组件
     */
    private void initViews(View view) {
        btnSaveScene = view.findViewById(R.id.btn_save_scene);
        btnLoadScene = view.findViewById(R.id.btn_load_scene);

        btnDeleteScene = view.findViewById(R.id.btn_delete_scene);

        // 初始化系统场景按钮
        btnBiologicalScene = view.findViewById(R.id.btn_biological_scene);
        btnStereoscopicScene = view.findViewById(R.id.btn_stereoscopic_scene);


        progressBar = view.findViewById(R.id.progress_bar);

        // 初始化ISP调节容器
        ispAdjustLayout = view.findViewById(R.id.isp_adjust_container);

        // 初始状态设置
        progressBar.setVisibility(View.GONE);

        // 初始化系统场景按钮状态
        updateSystemSceneButtonStates();
    }
    
    /**
     * 设置按钮监听器
     */
    private void setupListeners() {
        btnSaveScene.setOnClickListener(v -> executeSaveSceneTest());
        btnLoadScene.setOnClickListener(v -> executeLoadSceneTest());

        btnDeleteScene.setOnClickListener(v -> showDeleteSceneDialog());

        // 系统场景按钮监听器（使用新的统一API）
        btnBiologicalScene.setOnClickListener(v -> applySceneWithUI("生物镜"));
        btnStereoscopicScene.setOnClickListener(v -> applySceneWithUI("体视镜"));

        // 初始化ISP调节界面
        initIspAdjustInterface();

        // 初始化系统场景
        initializeSystemScenes();
    }
    
    // ===== 测试功能实现 =====
    
    /**
     * 执行保存场景测试
     */
    private void executeSaveSceneTest() {
        if (isTestRunning) return;

        // 显示场景名称输入对话框
        showSaveSceneNameDialog();
    }

    /**
     * 显示保存场景名称输入对话框
     */
    private void showSaveSceneNameDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(requireContext());
        builder.setTitle("保存ISP场景");
        builder.setMessage("请输入场景名称：");

        // 创建输入框
        final android.widget.EditText input = new android.widget.EditText(requireContext());
        input.setHint("请输入场景名称");

        // 预填充默认名称
        String defaultName = generateDefaultSceneName();
        input.setText(defaultName);
        input.selectAll(); // 选中所有文本，方便用户修改

        // 设置输入框样式
        input.setPadding(50, 30, 50, 30);
        builder.setView(input);

        builder.setPositiveButton("保存", null); // 先设为null，后面重新设置以防止自动关闭
        builder.setNegativeButton("取消", (dialog, which) -> dialog.dismiss());

        AlertDialog dialog = builder.create();
        dialog.show();

        // 重新设置确定按钮的点击事件，以便进行验证
        dialog.getButton(AlertDialog.BUTTON_POSITIVE).setOnClickListener(v -> {
            String sceneName = input.getText().toString().trim();

            if (sceneName.isEmpty()) {
                showToast("场景名称不能为空");
                input.requestFocus();
                return;
            }

            // 检查场景名称是否已存在
            List<String> existingScenes = TpIspParam.getAllSceneNames();
            if (existingScenes.contains(sceneName)) {
                showToast("场景名称已存在，请使用其他名称");
                input.selectAll();
                input.requestFocus();
                return;
            }

            // 验证通过，关闭对话框并执行保存
            dialog.dismiss();
            performSaveScene(sceneName);
        });

        // 自动弹出键盘
        input.requestFocus();
        dialog.getWindow().setSoftInputMode(android.view.WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE);
    }

    /**
     * 生成默认场景名称
     */
    private String generateDefaultSceneName() {
        SimpleDateFormat sdf = new SimpleDateFormat("MMdd_HHmm", Locale.getDefault());
        return "场景_" + sdf.format(new Date());
    }

    /**
     * 执行保存场景操作
     */
    private void performSaveScene(String sceneName) {
        setTestRunning(true);

        executeAsyncTest(() -> {
            try {
                int savedCount = TpIspParam.saveCurrentAsScene(sceneName);

                mainHandler.post(() -> {
                    showToast(savedCount > 0 ? "场景保存成功" : "场景保存失败");
                    setTestRunning(false);
                });

            } catch (Exception e) {
                mainHandler.post(() -> {
                    showToast("场景保存失败");
                    setTestRunning(false);
                });
            }
        });
    }
    
    /**
     * 执行加载场景测试
     */
    private void executeLoadSceneTest() {
        if (isTestRunning) return;

        setTestRunning(true);

        executeAsyncTest(() -> {
            try {
                List<String> sceneNames = TpIspParam.getAllSceneNames();

                mainHandler.post(() -> {
                    setTestRunning(false);

                    if (sceneNames.isEmpty()) {
                        showToast("没有可加载的场景，请先保存场景");
                        return;
                    }

                    // 显示场景选择对话框
                    showLoadSceneSelectionDialog(sceneNames);
                });

            } catch (Exception e) {
                mainHandler.post(() -> {
                    showToast("获取场景列表失败");
                    setTestRunning(false);
                });
            }
        });
    }

    /**
     * 显示加载场景选择对话框
     */
    private void showLoadSceneSelectionDialog(List<String> sceneNames) {
        AlertDialog.Builder builder = new AlertDialog.Builder(requireContext());

        // 设置标题，包含场景数量信息
        String title = "选择要加载的场景 (" + sceneNames.size() + "个)";
        builder.setTitle(title);

        // 为场景名称添加类型标识
        String[] displayNames = new String[sceneNames.size()];
        for (int i = 0; i < sceneNames.size(); i++) {
            String sceneName = sceneNames.get(i);
            boolean isSystemScene = TpIspParam.isSystemProtectedScene(sceneName);

            if (isSystemScene) {
                // 系统保护场景
                displayNames[i] = "🔬 " + sceneName + " (系统场景)";
            } else {
                // 用户自定义场景
                displayNames[i] = "👤 " + sceneName + " (用户场景)";
            }
        }

        // 设置单选列表
        builder.setItems(displayNames, (dialog, which) -> {
            String selectedScene = sceneNames.get(which);
            dialog.dismiss();

            // 使用统一的场景应用API
            applySceneWithUI(selectedScene);
        });

        // 添加取消按钮
        builder.setNegativeButton("取消", (dialog, which) -> {
            dialog.dismiss();
        });

        // 添加查看详情按钮
        builder.setNeutralButton("查看详情", (dialog, which) -> {
            dialog.dismiss();
            showSceneDetailsDialog(sceneNames);
        });

        AlertDialog dialog = builder.create();
        dialog.show();
    }

    /**
     * 显示场景详情对话框
     */
    private void showSceneDetailsDialog(List<String> sceneNames) {
        StringBuilder details = new StringBuilder();
        details.append("📋 场景详细信息\n\n");
        details.append("📊 统计信息:\n");
        details.append("• 总场景数: ").append(sceneNames.size()).append(" 个\n");
        details.append("• 总场景数: ").append(sceneNames.size()).append(" 个\n\n");
        details.append("📝 场景列表:\n");

        for (int i = 0; i < sceneNames.size(); i++) {
            details.append(String.format("  %d. %s\n", i + 1, sceneNames.get(i)));
        }

        details.append("\n💡 提示: 点击'加载场景'按钮可以选择具体场景进行加载测试");

        showToast("获取到 " + sceneNames.size() + " 个场景");
    }

    /**
     * 执行加载场景操作
     */
    private void performLoadScene(String sceneName, int totalScenes) {
        setTestRunning(true);

        executeAsyncTest(() -> {
            try {
                int appliedCount = TpIspParam.applyScene(sceneName);

                mainHandler.post(() -> {
                    showToast(appliedCount > 0 ? "场景加载成功" : "场景加载失败");
                    setTestRunning(false);
                });

            } catch (Exception e) {
                mainHandler.post(() -> {
                    showToast("场景加载失败");
                    setTestRunning(false);
                });
            }
        });
    }





    /**
     * 显示删除场景对话框
     */
    private void showDeleteSceneDialog() {
        List<String> allScenes = TpIspParam.getAllSceneNames();

        if (allScenes.isEmpty()) {
            showToast("没有可删除的场景");
            return;
        }

        // 创建场景选择对话框
        String[] sceneArray = allScenes.toArray(new String[0]);

        new AlertDialog.Builder(requireContext())
                .setTitle("🗑️ 选择要删除的场景")
                .setItems(sceneArray, (dialog, which) -> {
                    String selectedScene = sceneArray[which];
                    showDeleteConfirmDialog(selectedScene);
                })
                .setNegativeButton("取消", null)
                .setIcon(android.R.drawable.ic_menu_delete)
                .show();
    }

    /**
     * 显示删除确认对话框
     */
    private void showDeleteConfirmDialog(String sceneName) {
        new AlertDialog.Builder(requireContext())
                .setTitle("⚠️ 确认删除场景")
                .setMessage("确定要删除场景 \"" + sceneName + "\" 吗？\n\n" +
                           "此操作将永久删除:\n" +
                           "• 场景中的所有ISP参数设置\n" +
                           "• 场景的保存时间信息\n" +
                           "• 无法恢复删除的数据\n\n" +
                           "⚠️ 此操作不可撤销!")
                .setPositiveButton("确定删除", (dialog, which) -> performDeleteScene(sceneName))
                .setNegativeButton("取消", null)
                .setIcon(android.R.drawable.ic_dialog_alert)
                .show();
    }

    /**
     * 执行删除场景操作
     */
    private void performDeleteScene(String sceneName) {
        if (isTestRunning) return;

        setTestRunning(true);

        executeAsyncTest(() -> {
            try {
                boolean success = TpIspParam.deleteScene(sceneName);

                mainHandler.post(() -> {
                    showToast(success ? "场景删除成功" : "场景删除失败");
                    setTestRunning(false);
                });

            } catch (Exception e) {
                mainHandler.post(() -> {
                    showToast("场景删除失败");
                    setTestRunning(false);
                });
            }
        });
    }



    // ===== 辅助方法 =====

    /**
     * 删除指定名称的场景
     * <p>
     * 提供给外部调用的场景删除接口，支持删除单个场景。
     * </p>
     *
     * @param sceneName 要删除的场景名称
     * @return true表示删除成功，false表示删除失败
     */
    public boolean deleteScene(String sceneName) {
        if (sceneName == null || sceneName.trim().isEmpty()) {
            showToast("场景名称不能为空");
            return false;
        }

        try {
            boolean success = TpIspParam.deleteScene(sceneName);
            showToast(success ? "场景删除成功" : "场景删除失败");
            return success;

        } catch (Exception e) {
            showToast("删除场景时发生异常");
            return false;
        }
    }

    /**
     * 批量删除场景
     * <p>
     * 提供给外部调用的批量场景删除接口。
     * </p>
     *
     * @param sceneNames 要删除的场景名称列表
     * @return 成功删除的场景数量
     */
    public int deleteScenes(List<String> sceneNames) {
        if (sceneNames == null || sceneNames.isEmpty()) {
            showToast("场景列表不能为空");
            return 0;
        }

        try {
            int deletedCount = TpIspParam.deleteScenes(sceneNames);
            showToast("批量删除完成: " + deletedCount + "/" + sceneNames.size());
            return deletedCount;

        } catch (Exception e) {
            showToast("批量删除场景时发生异常");
            return 0;
        }
    }

    /**
     * 异步执行测试任务
     */
    private void executeAsyncTest(Runnable testTask) {
        new Thread(testTask).start();
    }

    /**
     * 设置测试运行状态
     */
    private void setTestRunning(boolean running) {
        isTestRunning = running;

        // 更新UI状态
        btnSaveScene.setEnabled(!running);
        btnLoadScene.setEnabled(!running);
        btnDeleteScene.setEnabled(!running);

        progressBar.setVisibility(running ? View.VISIBLE : View.GONE);
    }



    /**
     * 显示Toast提示
     */
    private void showToast(String message) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show();
    }





    // ===== ISP参数调节功能 =====

    /**
     * 初始化ISP参数调节界面
     */
    private void initIspAdjustInterface() {
        try {
            // 初始化参数控件映射
            parameterControlMap = new java.util.HashMap<>();

            // 创建ISP参数变化监听器
            createIspDataChangedListener();

            // 创建完整的ISP参数调节界面
            createCompleteIspInterface();

            // ISP调节界面初始化完成

        } catch (Exception e) {
            showToast("ISP调节界面初始化失败");
        }
    }

    /**
     * 创建ISP参数变化监听器
     */
    private void createIspDataChangedListener() {
        ispDataChangedListener = new TpIspParam.OnDataChangedListener() {
            @Override
            public void onDataChanged(TpIspParam param, int newValue) {
                // 在主线程中更新UI
                mainHandler.post(() -> {
                    updateParameterControl(param, newValue);
                });
            }

            @Override
            public void onLongDataChanged(TpIspParam param, long newValue) {
                // 处理长整型参数变化（如果需要）
                mainHandler.post(() -> {
                    updateParameterControl(param, (int) newValue);
                });
            }
        };

        // 注册监听器
        TpIspParam.addOnDataChangedListener(ispDataChangedListener);
    }

    /**
     * 更新参数控件显示
     */
    private void updateParameterControl(TpIspParam param, int newValue) {
        // 1. 更新SeekBar控件
        ParameterControlPair controlPair = parameterControlMap.get(param);
        if (controlPair != null) {
            // 更新数值显示
            controlPair.valueView.setText(String.valueOf(newValue));

            // 更新滑块位置（避免触发监听器）
            controlPair.seekBar.setOnSeekBarChangeListener(null);
            controlPair.seekBar.setProgress(newValue);
            controlPair.seekBar.setOnSeekBarChangeListener(controlPair.changeListener);
        }

        // 2. 检测特殊参数并更新对应UI组件
        if (param == TpIspParam.TOUPTEK_PARAM_WBCHOICE && wbRadioGroup != null) {
            // 更新白平衡RadioGroup状态
            wbRadioGroup.setOnCheckedChangeListener(null); // 临时移除监听器
            switch (newValue) {
                case 0: // 手动
                    wbRadioGroup.check(wbRadioGroup.getChildAt(1).getId()); // 修正：索引1是Manual按钮
                    break;
                case 1: // 自动
                    wbRadioGroup.check(wbRadioGroup.getChildAt(0).getId()); // 修正：索引0是Auto按钮
                    break;
                case 2: // ROI
                    wbRadioGroup.check(wbRadioGroup.getChildAt(2).getId());
                    break;
            }
            // 恢复监听器 - 修正索引映射
            wbRadioGroup.setOnCheckedChangeListener((group, checkedId) -> {
                if (checkedId == wbRadioGroup.getChildAt(0).getId()) { // 修正：索引0是Auto按钮
                    TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_WBCHOICE, 1);
                } else if (checkedId == wbRadioGroup.getChildAt(1).getId()) { // 修正：索引1是Manual按钮
                    TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_WBCHOICE, 0);
                } else if (checkedId == wbRadioGroup.getChildAt(2).getId()) {
                    TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_WBCHOICE, 2);
                }
            });

            // 同时更新白平衡相关滑块的启用/禁用状态
            if (newValue == 0) {
                // 手动白平衡模式 - 启用RGB增益调节
                enableSeekBar(TpIspParam.TOUPTEK_PARAM_WBREDGAIN);
                enableSeekBar(TpIspParam.TOUPTEK_PARAM_WBGREENGAIN);
                enableSeekBar(TpIspParam.TOUPTEK_PARAM_WBBLUEGAIN);
            } else {
                // 自动或ROI白平衡模式 - 禁用RGB增益调节
                disableSeekBar(TpIspParam.TOUPTEK_PARAM_WBREDGAIN);
                disableSeekBar(TpIspParam.TOUPTEK_PARAM_WBGREENGAIN);
                disableSeekBar(TpIspParam.TOUPTEK_PARAM_WBBLUEGAIN);
            }
        }

        if (param == TpIspParam.TOUPTEK_PARAM_EXPOSURECHOICE && exposureCheckBox != null) {
            // 更新自动曝光CheckBox状态
            exposureCheckBox.setOnCheckedChangeListener(null); // 临时移除监听器
            exposureCheckBox.setChecked(newValue == 1); // 1=自动，0=手动
            // 恢复监听器
            exposureCheckBox.setOnCheckedChangeListener((buttonView, isChecked) -> {
                TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_EXPOSURECHOICE, isChecked ? 1 : 0);
            });

            // 同时更新曝光相关滑块的启用/禁用状态
            if (newValue == 1) {
                // 自动曝光模式 - 启用曝光补偿，禁用手动曝光参数
                enableSeekBar(TpIspParam.TOUPTEK_PARAM_EXPOSURECOMPENSATION);
                disableSeekBar(TpIspParam.TOUPTEK_PARAM_EXPOSURETIME);
                disableSeekBar(TpIspParam.TOUPTEK_PARAM_EXPOSUREGAIN);
            } else {
                // 手动曝光模式 - 禁用曝光补偿，启用手动曝光参数
                disableSeekBar(TpIspParam.TOUPTEK_PARAM_EXPOSURECOMPENSATION);
                enableSeekBar(TpIspParam.TOUPTEK_PARAM_EXPOSURETIME);
                enableSeekBar(TpIspParam.TOUPTEK_PARAM_EXPOSUREGAIN);
            }
        }
    }









    /**
     * 创建完整的ISP参数调节界面
     */
    private void createCompleteIspInterface() {
        if (ispAdjustLayout == null) return;

        // 清空容器
        ispAdjustLayout.removeAllViews();

        // 1. 曝光控制组 - 使用XCamView的CheckBox模式选择方式
        addExposureControlGroup();

        // 2. 白平衡控制组 - 使用XCamView的RadioGroup模式选择方式
        addWhiteBalanceControlGroup();

        // 3. 图像处理组
        addIspGroup("🎨 图像处理", new IspParamInfo[]{
            new IspParamInfo("亮度", TpIspParam.TOUPTEK_PARAM_BRIGHTNESS),
            new IspParamInfo("对比度", TpIspParam.TOUPTEK_PARAM_CONTRAST),
            new IspParamInfo("饱和度", TpIspParam.TOUPTEK_PARAM_SATURATION),
            new IspParamInfo("色调", TpIspParam.TOUPTEK_PARAM_HUE),
            new IspParamInfo("Gamma", TpIspParam.TOUPTEK_PARAM_GAMMA),
            new IspParamInfo("锐度", TpIspParam.TOUPTEK_PARAM_SHARPNESS)
        });

        // 4. 降噪和增强组
        addIspGroup("🔧 降噪增强", new IspParamInfo[]{
            new IspParamInfo("降噪", TpIspParam.TOUPTEK_PARAM_DENOISE),
            new IspParamInfo("暗部增强", TpIspParam.TOUPTEK_PARAM_DARKENHANCE),
            new IspParamInfo("宽动态范围", TpIspParam.TOUPTEK_PARAM_WDREXPRATIO),
            new IspParamInfo("低动态对比度", TpIspParam.TOUPTEK_PARAM_LDCRATIO)
        });

        // 5. 色彩校正组
        addIspGroup("🌈 色彩校正", new IspParamInfo[]{
            new IspParamInfo("色彩模式", TpIspParam.TOUPTEK_PARAM_COLORORGRAY),
            new IspParamInfo("色彩色调", TpIspParam.TOUPTEK_PARAM_COLORTONE),
            new IspParamInfo("色温红增益", TpIspParam.TOUPTEK_PARAM_CTREDGAIN),
            new IspParamInfo("色温绿增益", TpIspParam.TOUPTEK_PARAM_CTGREENGAIN),
            new IspParamInfo("色温蓝增益", TpIspParam.TOUPTEK_PARAM_CTBLUEGAIN)
        });

        // 6. 其他设置组
        addIspGroup("⚙️ 其他设置", new IspParamInfo[]{
            new IspParamInfo("镜像效果", TpIspParam.TOUPTEK_PARAM_MIRROR),
            new IspParamInfo("翻转效果", TpIspParam.TOUPTEK_PARAM_FLIP),
            new IspParamInfo("频率设置", TpIspParam.TOUPTEK_PARAM_HZ),
            new IspParamInfo("带宽控制", TpIspParam.TOUPTEK_PARAM_BANDWIDTH),
            new IspParamInfo("场景模式", TpIspParam.TOUPTEK_PARAM_ISP_DEFAULT_TYPE)
        });

        // 初始化完成后，设置初始的控件状态
        mainHandler.postDelayed(() -> {
            android.util.Log.d("TpTestDialog", "初始化完成，设置初始控件状态");
            initializeControlStates();
        }, 300); // 延迟确保所有控件创建完成


    }

    /**
     * 添加白平衡控制组 - 参考XCamView的TpWBDialogFragment实现
     */
    private void addWhiteBalanceControlGroup() {
        // 组标题
        android.widget.TextView titleView = new android.widget.TextView(requireContext());
        titleView.setText("⚪ 白平衡");
        titleView.setTextSize(11);
        titleView.setTypeface(null, android.graphics.Typeface.BOLD);
        titleView.setPadding(0, 8, 0, 4);
        titleView.setTextColor(0xFF2196F3);
        ispAdjustLayout.addView(titleView);

        // 白平衡模式RadioGroup - 参考XCamView的实现
        wbRadioGroup = new android.widget.RadioGroup(requireContext());
        wbRadioGroup.setOrientation(android.widget.RadioGroup.HORIZONTAL);

        // 创建RadioButton
        android.widget.RadioButton radioAuto = new android.widget.RadioButton(requireContext());
        radioAuto.setId(android.view.View.generateViewId());
        radioAuto.setText("Auto");
        radioAuto.setTextSize(9);
        radioAuto.setTextColor(0xFF333333);

        android.widget.RadioButton radioManual = new android.widget.RadioButton(requireContext());
        radioManual.setId(android.view.View.generateViewId());
        radioManual.setText("Manual");
        radioManual.setTextSize(9);
        radioManual.setTextColor(0xFF333333);

        android.widget.RadioButton radioRoi = new android.widget.RadioButton(requireContext());
        radioRoi.setId(android.view.View.generateViewId());
        radioRoi.setText("ROI");
        radioRoi.setTextSize(9);
        radioRoi.setTextColor(0xFF333333);

        wbRadioGroup.addView(radioAuto);
        wbRadioGroup.addView(radioManual);
        wbRadioGroup.addView(radioRoi);

        // 设置初始状态
        int savedSelection = TpIspParam.getCurrentValue(TpIspParam.TOUPTEK_PARAM_WBCHOICE);
        switch (savedSelection) {
            case 0:
                wbRadioGroup.check(radioManual.getId());
                break;
            case 1:
                wbRadioGroup.check(radioAuto.getId());
                break;
            case 2:
                wbRadioGroup.check(radioRoi.getId());
                break;
        }

        // 设置监听器 - 参考XCamView的逻辑
        wbRadioGroup.setOnCheckedChangeListener((group, checkedId) -> {
            if (checkedId == radioAuto.getId()) {
                android.util.Log.d("TpTestDialog", "白平衡模式切换: 自动");
                TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_WBCHOICE, 1);
                disableSeekBar(TpIspParam.TOUPTEK_PARAM_WBREDGAIN);
                disableSeekBar(TpIspParam.TOUPTEK_PARAM_WBGREENGAIN);
                disableSeekBar(TpIspParam.TOUPTEK_PARAM_WBBLUEGAIN);
            } else if (checkedId == radioManual.getId()) {
                android.util.Log.d("TpTestDialog", "白平衡模式切换: 手动");
                TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_WBCHOICE, 0);
                enableSeekBar(TpIspParam.TOUPTEK_PARAM_WBREDGAIN);
                enableSeekBar(TpIspParam.TOUPTEK_PARAM_WBGREENGAIN);
                enableSeekBar(TpIspParam.TOUPTEK_PARAM_WBBLUEGAIN);
            } else if (checkedId == radioRoi.getId()) {
                android.util.Log.d("TpTestDialog", "白平衡模式切换: ROI");
                TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_WBCHOICE, 2);
                disableSeekBar(TpIspParam.TOUPTEK_PARAM_WBREDGAIN);
                disableSeekBar(TpIspParam.TOUPTEK_PARAM_WBGREENGAIN);
                disableSeekBar(TpIspParam.TOUPTEK_PARAM_WBBLUEGAIN);
            }
        });

        ispAdjustLayout.addView(wbRadioGroup);

        // 添加白平衡参数控件
        addIspGroup("", new IspParamInfo[]{
            new IspParamInfo("红色增益", TpIspParam.TOUPTEK_PARAM_WBREDGAIN),
            new IspParamInfo("绿色增益", TpIspParam.TOUPTEK_PARAM_WBGREENGAIN),
            new IspParamInfo("蓝色增益", TpIspParam.TOUPTEK_PARAM_WBBLUEGAIN)
        });
    }

    /**
     * 添加ISP参数组 - 紧凑版
     */
    private void addIspGroup(String groupTitle, IspParamInfo[] params) {
        // 组标题 - 更紧凑
        android.widget.TextView titleView = new android.widget.TextView(requireContext());
        titleView.setText(groupTitle);
        titleView.setTextSize(11);
        titleView.setTypeface(null, android.graphics.Typeface.BOLD);
        titleView.setPadding(0, 8, 0, 4);
        titleView.setTextColor(0xFF2196F3); // 蓝色
        ispAdjustLayout.addView(titleView);

        // 添加参数控件 - 左右两列布局
        for (int i = 0; i < params.length; i += 2) {
            android.widget.LinearLayout rowLayout = new android.widget.LinearLayout(requireContext());
            rowLayout.setOrientation(android.widget.LinearLayout.HORIZONTAL);
            rowLayout.setPadding(0, 2, 0, 2);

            // 左列参数
            android.widget.LinearLayout leftParam = createCompactParameterControl(params[i]);
            android.widget.LinearLayout.LayoutParams leftParams =
                new android.widget.LinearLayout.LayoutParams(0,
                    android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
            leftParams.setMargins(0, 0, 4, 0);
            rowLayout.addView(leftParam, leftParams);

            // 右列参数（如果存在）
            if (i + 1 < params.length) {
                android.widget.LinearLayout rightParam = createCompactParameterControl(params[i + 1]);
                android.widget.LinearLayout.LayoutParams rightParams =
                    new android.widget.LinearLayout.LayoutParams(0,
                        android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
                rightParams.setMargins(4, 0, 0, 0);
                rowLayout.addView(rightParam, rightParams);
            } else {
                // 如果是奇数个参数，右侧留空
                android.view.View spacer = new android.view.View(requireContext());
                android.widget.LinearLayout.LayoutParams spacerParams =
                    new android.widget.LinearLayout.LayoutParams(0,
                        android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
                rowLayout.addView(spacer, spacerParams);
            }

            ispAdjustLayout.addView(rowLayout);
        }

        // 添加分组分割线 - 更细
        android.view.View divider = new android.view.View(requireContext());
        divider.setBackgroundColor(0xFFE0E0E0);
        android.widget.LinearLayout.LayoutParams dividerParams =
            new android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.MATCH_PARENT, 1);
        dividerParams.setMargins(0, 4, 0, 0);
        ispAdjustLayout.addView(divider, dividerParams);
    }



    /**
     * 创建紧凑版参数控件 - 用于两列布局，支持实时监听
     */
    private android.widget.LinearLayout createCompactParameterControl(IspParamInfo paramInfo) {
        android.widget.LinearLayout layout = new android.widget.LinearLayout(requireContext());
        layout.setOrientation(android.widget.LinearLayout.VERTICAL);
        layout.setPadding(4, 4, 4, 4);

        // 参数名称和当前值 - 紧凑版
        android.widget.LinearLayout headerLayout = new android.widget.LinearLayout(requireContext());
        headerLayout.setOrientation(android.widget.LinearLayout.HORIZONTAL);

        android.widget.TextView nameView = new android.widget.TextView(requireContext());
        nameView.setText(paramInfo.displayName);
        nameView.setTextSize(10);
        nameView.setTypeface(null, android.graphics.Typeface.BOLD);

        android.widget.TextView valueView = new android.widget.TextView(requireContext());
        int currentValue = TpIspParam.getCurrentValue(paramInfo.param);
        valueView.setText(String.valueOf(currentValue));
        valueView.setTextSize(10);
        valueView.setGravity(android.view.Gravity.END);

        android.widget.LinearLayout.LayoutParams nameParams =
            new android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
        android.widget.LinearLayout.LayoutParams valueParams =
            new android.widget.LinearLayout.LayoutParams(android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT);

        headerLayout.addView(nameView, nameParams);
        headerLayout.addView(valueView, valueParams);

        // 滑块控件 - 紧凑版
        android.widget.SeekBar seekBar = new android.widget.SeekBar(requireContext());
        seekBar.setMin(TpIspParam.getMinValue(paramInfo.param));
        seekBar.setMax(TpIspParam.getMaxValue(paramInfo.param));
        seekBar.setProgress(currentValue);

        // 设置滑块高度更小
        android.widget.LinearLayout.LayoutParams seekBarParams =
            new android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.MATCH_PARENT, 40);
        seekBar.setLayoutParams(seekBarParams);

        // 滑块变化监听器
        android.widget.SeekBar.OnSeekBarChangeListener changeListener = new android.widget.SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(android.widget.SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    // 实时更新参数值
                    TpIspParam.updateParam(paramInfo.param, progress);
                    valueView.setText(String.valueOf(progress));

                    // 调试信息
                    android.util.Log.d("TpTestDialog", "用户调节参数: " + paramInfo.param.name() + " = " + progress);
                }
            }

            @Override
            public void onStartTrackingTouch(android.widget.SeekBar seekBar) {
                android.util.Log.d("TpTestDialog", "开始拖动参数: " + paramInfo.param.name());
            }

            @Override
            public void onStopTrackingTouch(android.widget.SeekBar seekBar) {
                android.util.Log.d("TpTestDialog", "停止拖动参数: " + paramInfo.param.name() + " = " + seekBar.getProgress());
            }
        };

        seekBar.setOnSeekBarChangeListener(changeListener);

        // 将控件信息保存到映射中，用于实时更新
        ParameterControlPair controlPair = new ParameterControlPair(valueView, seekBar, changeListener);
        parameterControlMap.put(paramInfo.param, controlPair);

        layout.addView(headerLayout);
        layout.addView(seekBar);

        return layout;
    }

    // ===== XCamView风格的控件启用/禁用方法 =====

    /**
     * 启用SeekBar控件 - 参考XCamView的enableSeekBar实现
     */
    private void enableSeekBar(TpIspParam param) {
        ParameterControlPair controlPair = parameterControlMap.get(param);
        if (controlPair != null) {
            controlPair.seekBar.setEnabled(true);
            controlPair.seekBar.setAlpha(1.0f);
            controlPair.valueView.setAlpha(1.0f);
            android.util.Log.d("TpTestDialog", "启用控件: " + param.name());
        } else {
            android.util.Log.w("TpTestDialog", "找不到控件: " + param.name());
        }
    }

    /**
     * 禁用SeekBar控件 - 参考XCamView的disableSeekBar实现
     */
    private void disableSeekBar(TpIspParam param) {
        ParameterControlPair controlPair = parameterControlMap.get(param);
        if (controlPair != null) {
            controlPair.seekBar.setEnabled(false);
            controlPair.seekBar.setAlpha(0.5f);
            controlPair.valueView.setAlpha(0.5f);
            android.util.Log.d("TpTestDialog", "禁用控件: " + param.name());
        } else {
            android.util.Log.w("TpTestDialog", "找不到控件: " + param.name());
        }
    }

    /**
     * 初始化控件状态 - 参考XCamView的初始化逻辑
     */
    private void initializeControlStates() {
        android.util.Log.d("TpTestDialog", "=== 初始化控件状态 ===");

        // 1. 根据当前曝光模式设置控件状态
        int exposureMode = TpIspParam.getCurrentValue(TpIspParam.TOUPTEK_PARAM_EXPOSURECHOICE);
        android.util.Log.d("TpTestDialog", "当前曝光模式: " + exposureMode);

        if (exposureMode == 1) {
            // 自动曝光模式
            enableSeekBar(TpIspParam.TOUPTEK_PARAM_EXPOSURECOMPENSATION);
            disableSeekBar(TpIspParam.TOUPTEK_PARAM_EXPOSURETIME);
            disableSeekBar(TpIspParam.TOUPTEK_PARAM_EXPOSUREGAIN);
        } else {
            // 手动曝光模式
            disableSeekBar(TpIspParam.TOUPTEK_PARAM_EXPOSURECOMPENSATION);
            enableSeekBar(TpIspParam.TOUPTEK_PARAM_EXPOSURETIME);
            enableSeekBar(TpIspParam.TOUPTEK_PARAM_EXPOSUREGAIN);
        }

        // 2. 根据当前白平衡模式设置控件状态
        int wbChoice = TpIspParam.getCurrentValue(TpIspParam.TOUPTEK_PARAM_WBCHOICE);
        android.util.Log.d("TpTestDialog", "当前白平衡模式: " + wbChoice);

        if (wbChoice == 0) {
            // 手动白平衡模式
            enableSeekBar(TpIspParam.TOUPTEK_PARAM_WBREDGAIN);
            enableSeekBar(TpIspParam.TOUPTEK_PARAM_WBGREENGAIN);
            enableSeekBar(TpIspParam.TOUPTEK_PARAM_WBBLUEGAIN);
        } else {
            // 自动或ROI白平衡模式
            disableSeekBar(TpIspParam.TOUPTEK_PARAM_WBREDGAIN);
            disableSeekBar(TpIspParam.TOUPTEK_PARAM_WBGREENGAIN);
            disableSeekBar(TpIspParam.TOUPTEK_PARAM_WBBLUEGAIN);
        }

        android.util.Log.d("TpTestDialog", "=== 控件状态初始化完成 ===");
    }

    // ===== XCamView风格的模式控制实现 =====

    private android.widget.CheckBox exposureCheckBox;
    private android.widget.RadioGroup wbRadioGroup;

    /**
     * 添加曝光控制组 - 参考XCamView的TpAEDialogFragment实现
     */
    private void addExposureControlGroup() {
        // 组标题
        android.widget.TextView titleView = new android.widget.TextView(requireContext());
        titleView.setText("📸 曝光控制");
        titleView.setTextSize(11);
        titleView.setTypeface(null, android.graphics.Typeface.BOLD);
        titleView.setPadding(0, 8, 0, 4);
        titleView.setTextColor(0xFF2196F3);
        ispAdjustLayout.addView(titleView);

        // 自动曝光CheckBox - 参考XCamView的实现
        exposureCheckBox = new android.widget.CheckBox(requireContext());
        exposureCheckBox.setText("自动曝光");
        exposureCheckBox.setTextSize(10);
        exposureCheckBox.setTextColor(0xFF333333);

        // 设置初始状态
        int autoExposureValue = TpIspParam.getCurrentValue(TpIspParam.TOUPTEK_PARAM_EXPOSURECHOICE);
        exposureCheckBox.setChecked(autoExposureValue == 1);

        // 设置监听器 - 参考XCamView的逻辑
        exposureCheckBox.setOnCheckedChangeListener((buttonView, isChecked) -> {
            android.util.Log.d("TpTestDialog", "曝光模式切换: " + (isChecked ? "自动" : "手动"));

            if (isChecked) {
                // 自动曝光模式
                TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_EXPOSURECHOICE, 1);
                enableSeekBar(TpIspParam.TOUPTEK_PARAM_EXPOSURECOMPENSATION);
                disableSeekBar(TpIspParam.TOUPTEK_PARAM_EXPOSURETIME);
                disableSeekBar(TpIspParam.TOUPTEK_PARAM_EXPOSUREGAIN);
            } else {
                // 手动曝光模式
                TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_EXPOSURECHOICE, 0);
                disableSeekBar(TpIspParam.TOUPTEK_PARAM_EXPOSURECOMPENSATION);
                enableSeekBar(TpIspParam.TOUPTEK_PARAM_EXPOSURETIME);
                enableSeekBar(TpIspParam.TOUPTEK_PARAM_EXPOSUREGAIN);
            }
        });

        ispAdjustLayout.addView(exposureCheckBox);

        // 添加曝光参数控件
        addIspGroup("", new IspParamInfo[]{
            new IspParamInfo("曝光补偿", TpIspParam.TOUPTEK_PARAM_EXPOSURECOMPENSATION),
            new IspParamInfo("曝光时间", TpIspParam.TOUPTEK_PARAM_EXPOSURETIME),
            new IspParamInfo("曝光增益", TpIspParam.TOUPTEK_PARAM_EXPOSUREGAIN)
        });
    }

    /**
     * 创建单个参数的调节控件 - 原版（保留兼容性）
     */
    private android.widget.LinearLayout createParameterControl(IspParamInfo paramInfo) {
        android.widget.LinearLayout layout = new android.widget.LinearLayout(requireContext());
        layout.setOrientation(android.widget.LinearLayout.VERTICAL);
        layout.setPadding(0, 12, 0, 12);

        // 参数名称和当前值
        android.widget.LinearLayout headerLayout = new android.widget.LinearLayout(requireContext());
        headerLayout.setOrientation(android.widget.LinearLayout.HORIZONTAL);

        android.widget.TextView nameView = new android.widget.TextView(requireContext());
        nameView.setText(paramInfo.displayName);
        nameView.setTextSize(14);
        nameView.setTypeface(null, android.graphics.Typeface.BOLD);

        android.widget.TextView valueView = new android.widget.TextView(requireContext());
        int currentValue = TpIspParam.getCurrentValue(paramInfo.param);
        valueView.setText(String.valueOf(currentValue));
        valueView.setTextSize(14);
        valueView.setGravity(android.view.Gravity.END);

        android.widget.LinearLayout.LayoutParams nameParams =
            new android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
        android.widget.LinearLayout.LayoutParams valueParams =
            new android.widget.LinearLayout.LayoutParams(android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT);

        headerLayout.addView(nameView, nameParams);
        headerLayout.addView(valueView, valueParams);

        // 滑块控件
        android.widget.SeekBar seekBar = new android.widget.SeekBar(requireContext());
        seekBar.setMin(TpIspParam.getMinValue(paramInfo.param));
        seekBar.setMax(TpIspParam.getMaxValue(paramInfo.param));
        seekBar.setProgress(currentValue);

        // 滑块变化监听器
        seekBar.setOnSeekBarChangeListener(new android.widget.SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(android.widget.SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    // 实时更新参数值
                    TpIspParam.updateParam(paramInfo.param, progress);
                    valueView.setText(String.valueOf(progress));
                }
            }

            @Override
            public void onStartTrackingTouch(android.widget.SeekBar seekBar) {
                // 开始拖动时的处理
            }

            @Override
            public void onStopTrackingTouch(android.widget.SeekBar seekBar) {
                // 停止拖动时的处理
                showToast(paramInfo.displayName + ": " + seekBar.getProgress());
            }
        });

        // 范围显示
        android.widget.LinearLayout rangeLayout = new android.widget.LinearLayout(requireContext());
        rangeLayout.setOrientation(android.widget.LinearLayout.HORIZONTAL);

        android.widget.TextView minView = new android.widget.TextView(requireContext());
        minView.setText(String.valueOf(TpIspParam.getMinValue(paramInfo.param)));
        minView.setTextSize(10);
        minView.setTextColor(0xFF666666);

        android.widget.TextView maxView = new android.widget.TextView(requireContext());
        maxView.setText(String.valueOf(TpIspParam.getMaxValue(paramInfo.param)));
        maxView.setTextSize(10);
        maxView.setTextColor(0xFF666666);
        maxView.setGravity(android.view.Gravity.END);

        android.widget.LinearLayout.LayoutParams minParams =
            new android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
        android.widget.LinearLayout.LayoutParams maxParams =
            new android.widget.LinearLayout.LayoutParams(android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT);

        rangeLayout.addView(minView, minParams);
        rangeLayout.addView(maxView, maxParams);

        layout.addView(headerLayout);
        layout.addView(seekBar);
        layout.addView(rangeLayout);

        return layout;
    }







    /**
     * ISP参数信息类
     */
    private static class IspParamInfo {
        final String displayName;
        final TpIspParam param;

        IspParamInfo(String displayName, TpIspParam param) {
            this.displayName = displayName;
            this.param = param;
        }
    }

    /**
     * 参数控件对信息类 - 用于实时更新
     */
    private static class ParameterControlPair {
        final android.widget.TextView valueView;
        final android.widget.SeekBar seekBar;
        final android.widget.SeekBar.OnSeekBarChangeListener changeListener;

        ParameterControlPair(android.widget.TextView valueView,
                           android.widget.SeekBar seekBar,
                           android.widget.SeekBar.OnSeekBarChangeListener changeListener) {
            this.valueView = valueView;
            this.seekBar = seekBar;
            this.changeListener = changeListener;
        }
    }

    // ===== 系统场景管理方法 =====

    /**
     * 初始化系统场景
     * <p>
     * 对话框打开时自动获取生物和体视场景的默认参数
     * </p>
     */
    private void initializeSystemScenes() {
        executeAsyncTest(() -> {
            try {
                mainHandler.post(() -> {
                    // 系统场景初始化完成，无需显示详细信息
                    setTestRunning(false);
                });

            } catch (Exception e) {
                mainHandler.post(() -> {
                    showToast("系统场景初始化失败");
                    setTestRunning(false);
                });
            }
        });
    }

    /**
     * 应用指定场景（统一入口）
     * <p>
     * 使用新的统一场景管理API，自动识别系统场景和用户场景
     * </p>
     */
    private void applySceneWithUI(String sceneName) {
        if (isTestRunning) {
            showToast("正在执行其他操作，请稍候...");
            return;
        }

        setTestRunning(true);

        executeAsyncTest(() -> {
            try {
                int appliedCount = TpIspParam.applyScene(sceneName);

                mainHandler.post(() -> {
                    if (appliedCount > 0) {
                        showToast("场景应用成功");
                    } else {
                        showToast("场景应用失败");
                    }
                    setTestRunning(false);
                });

            } catch (Exception e) {
                mainHandler.post(() -> {
                    showToast("场景应用失败");
                    setTestRunning(false);
                });
            }
        });
    }




    /**
     * 更新系统场景按钮状态
     * <p>
     * 由于ISP_DEFAULT_TYPE不代表当前场景状态，此方法仅重置按钮显示
     * </p>
     */
    private void updateSystemSceneButtonStates() {
        if (btnBiologicalScene == null || btnStereoscopicScene == null) {
            return;
        }

        // 重置按钮为默认状态，不显示"当前场景"
        btnBiologicalScene.setAlpha(1.0f);
        btnStereoscopicScene.setAlpha(1.0f);
        btnBiologicalScene.setText("生物");
        btnStereoscopicScene.setText("体视");
    }

}
