1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolder$androidx.fragment.app.DialogFragmentandroidx.fragment.app.Fragment(androidx.appcompat.app.AppCompatActivityandroid.view.Viewkotlin.Enum6com.touptek.xcamview.view.MeasurementOverlayView.Shape androidx.viewbinding.ViewBinding&com.touptek.xcamview.util.BaseActivity-android.view.View.OnAttachStateChangeListenerDcom.touptek.xcamview.activity.MainMenu.OnRectangleVisibilityListener8androidx.recyclerview.widget.RecyclerView.ItemDecorationScom.touptek.xcamview.activity.browse.TpCopyDirDialogFragment.OnMoveCompleteListenerRcom.touptek.xcamview.activity.settings.TpMiscSettingsFragment.OnModeChangeListener                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            