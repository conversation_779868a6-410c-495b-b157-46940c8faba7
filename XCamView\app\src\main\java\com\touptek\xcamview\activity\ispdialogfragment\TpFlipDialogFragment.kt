package com.touptek.xcamview.activity.ispdialogfragment
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.Switch
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.touptek.xcamview.R
import com.touptek.video.TpIspParam

class TpFlipDialogFragment : DialogFragment() {

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.flip_layout, container, false)

        initTpIspParam()
        initSwitch(view)
        initParameterValue(view)
        return view
    }

    override fun onStart() {
        super.onStart()
        val dialog = dialog
        if (dialog != null) {
            val window = dialog.window
            if (window != null) {
                window.setDimAmount(0f)
                window.setBackgroundDrawableResource(android.R.color.transparent)
                val params = window.attributes

                // 获取传递过来的位置和宽度参数
                val args = arguments
                if (args != null) {
                    val buttonX = args.getInt("x", 0)
                    val buttonY = args.getInt("y", 0)
                    val buttonWidth = args.getInt("width", 0)

                    // 设置对话框的位置，使其显示在按钮的右边
                    params.x = buttonX + buttonWidth + 25 // 右边偏移按钮宽度
                    params.y = buttonY - 45 // 同按钮的Y轴位置
                }

                params.width = WindowManager.LayoutParams.WRAP_CONTENT
                params.height = WindowManager.LayoutParams.WRAP_CONTENT
                params.gravity = Gravity.TOP or Gravity.START // 从左上角开始定位
                window.attributes = params
            }
        }
    }

    private fun initTpIspParam() {
//        TpIspParam.init(context)
//        // 设置串口状态变化监听器
//        TpIspParam.setOnSerialStateChangedListener { connected ->
//            if (connected){
//                showToast("串口已连接")
//            }else{
//                showToast("串口断开")
//            }
//        }
    }

    private fun showToast(message: String) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }

    private fun initSwitch(view: View) {
        val switchVertical = view.findViewById<Switch>(R.id.switch_vfip_tv)
        val switchHorzFlip = view.findViewById<Switch>(R.id.switch_hfip_tv)

        switchVertical.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_FLIP, 1)
            } else {



                TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_FLIP, 0)
            }
        }

        switchHorzFlip.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_MIRROR, 1)
            } else {
                TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_MIRROR, 0)
            }
        }

    }

    private fun initParameterValue(view: View) {
        //设置WB模式
        val HfipSwitch = view.findViewById<Switch>(R.id.switch_hfip_tv)
        val VfipSwitch = view.findViewById<Switch>(R.id.switch_vfip_tv)
        val HfipState = TpIspParam.getCurrentValue(TpIspParam.TOUPTEK_PARAM_MIRROR)
        val VfipState = TpIspParam.getCurrentValue(TpIspParam.TOUPTEK_PARAM_FLIP)

        when (VfipState) {
            1 -> VfipSwitch.isChecked = true  // 激活状态
            0 -> VfipSwitch.isChecked = false // 普通状态
        } // 处理异常值
        when (HfipState) {
            1 -> HfipSwitch.isChecked = true  // 激活状态
            0 -> HfipSwitch.isChecked = false // 普通状态
        } // 处理异常值


    }




}