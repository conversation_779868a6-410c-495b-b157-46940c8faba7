<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 测试按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🎛️ ISP场景管理"
            android:textSize="16sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="12dp"
            android:textColor="?android:attr/textColorPrimary" />

        <!-- 系统场景快速切换区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp"
            android:background="?android:attr/selectableItemBackground"
            android:padding="4dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="🔬 系统场景："
                android:textSize="12sp"
                android:textStyle="bold"
                android:gravity="center_vertical"
                android:textColor="?android:attr/textColorPrimary" />

            <Button
                android:id="@+id/btn_biological_scene"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:text="生物镜"
                android:textSize="10sp"
                android:layout_marginEnd="4dp"
                android:backgroundTint="@android:color/holo_green_light"
                android:textColor="@android:color/white"
                style="?android:attr/buttonStyleSmall" />

            <Button
                android:id="@+id/btn_stereoscopic_scene"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:text="体视镜"
                android:textSize="10sp"
                android:layout_marginStart="4dp"
                android:backgroundTint="@android:color/holo_blue_light"
                android:textColor="@android:color/white"
                style="?android:attr/buttonStyleSmall" />

        </LinearLayout>

        <!-- 场景管理按钮区域 - 4个核心按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <Button
                android:id="@+id/btn_save_scene"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                android:text="保存场景"
                android:textSize="11sp"
                android:padding="6dp"
                style="?android:attr/buttonStyleSmall" />

            <Button
                android:id="@+id/btn_load_scene"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginHorizontal="4dp"
                android:text="加载场景"
                android:textSize="11sp"
                android:padding="6dp"
                style="?android:attr/buttonStyleSmall" />

            <Button
                android:id="@+id/btn_list_scenes"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginHorizontal="4dp"
                android:text="场景列表"
                android:textSize="11sp"
                android:padding="6dp"
                style="?android:attr/buttonStyleSmall" />

            <Button
                android:id="@+id/btn_delete_scene"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:text="删除场景"
                android:textSize="11sp"
                android:padding="6dp"
                android:backgroundTint="@android:color/holo_orange_light"
                android:textColor="@android:color/white"
                style="?android:attr/buttonStyleSmall" />

        </LinearLayout>

    </LinearLayout>

    <!-- 分割线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="?android:attr/listDivider"
        android:layout_marginBottom="12dp" />

    <!-- 进度条 -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:visibility="gone"
        style="?android:attr/progressBarStyleHorizontal"
        android:indeterminate="true" />

    <!-- ISP参数调节区域 - 紧凑版 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="🎛️ ISP参数调节"
        android:textSize="12sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="6dp"
        android:textColor="?android:attr/textColorPrimary" />

    <!-- ISP参数调节控件容器 - 扩大高度 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="500dp"
        android:scrollbars="vertical"
        android:fadeScrollbars="false">

        <LinearLayout
            android:id="@+id/isp_adjust_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="4dp" />

    </ScrollView>

</LinearLayout>
