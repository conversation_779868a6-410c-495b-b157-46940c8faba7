<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <!-- 预览容器：包含TextureView、TV预览和ROI视图 -->
    <FrameLayout
        android:id="@+id/preview_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.touptek.ui.TpTextureView
            android:id="@+id/textureView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <!-- ROI选择框视图，叠加在TextureView之上 -->
        <com.touptek.ui.TpRoiView
            android:id="@+id/roiView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="invisible" />

    </FrameLayout>

    <!-- 悬浮控制面板 - 底部居中 -->
    <LinearLayout
        android:id="@+id/controlPanel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:orientation="horizontal"
        android:gravity="center"
        android:padding="16dp"
        android:layout_marginBottom="32dp">

        <Button
            android:id="@+id/captureButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="拍照"
            android:layout_margin="8dp" />

        <Button
            android:id="@+id/recordButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="录像"
            android:layout_margin="8dp" />

        <Button
            android:id="@+id/settingsButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="设置"
            android:layout_margin="8dp" />

        <Button
            android:id="@+id/resolutionTestButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="切换分辨率"
            android:layout_margin="8dp"
            android:textSize="12sp" />

        <Button
            android:id="@+id/browserButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="浏览器"
            android:layout_margin="8dp"
            android:textSize="12sp" />

        <Button
            android:id="@+id/roiButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="开启ROI"
            android:layout_margin="8dp"
            android:textSize="12sp" />

        <!-- 测试ISP按钮 - 移动到底部控制面板 -->
        <Button
            android:id="@+id/test_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="测试ISP"
            android:layout_margin="8dp"
            android:textSize="12sp"
            android:backgroundTint="@android:color/holo_purple"
            android:textColor="@android:color/white"
            style="?android:attr/buttonStyleSmall" />

    </LinearLayout>

</FrameLayout>